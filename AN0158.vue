<template>
  <div>
    <el-form ref="form" :model="form" :rules="rules" label-position="top" label-width="80px" size="mini">
      <el-form-item prop="p1" label="1、排尿困难">
        <el-radio-group v-model="form.p1">
          <el-radio label="是" border/>
          <el-radio label="否" border/>
        </el-radio-group>
      </el-form-item>
      <el-form-item v-if="form.p1 === '是'" prop="p1Onset" label="起病时间">
        <el-date-picker
          v-model="form.p1Onset"
          type="datetime"
          value-format="yyyy-MM-dd HH:mm:ss"
          placeholder="请选择起病时间"
          :editable="false"
          style="width: 200px"
        />
      </el-form-item>
      <el-form-item v-if="form.p1 === '是'" prop="p1Trend" label="变化趋势">
          <el-radio-group v-model="form.p1Trend" class="p1-trend-group">
            <el-radio label="进行性加重" border />
            <el-radio label="缓解" border />
            <el-radio label="其他" border />
          </el-radio-group>
          <el-input
            v-if="form.p1Trend === '其他'"
            v-model="form.p1TrendOther"
            placeholder=""
            maxlength="20"
            show-word-limit
            class="p1-trend-other"
          />
      </el-form-item>
      <el-form-item v-if="form.p1 === '是'" prop="p1Etiology" label="病因诱因">
        <el-radio-group v-model="form.p1Etiology" class="p1-etiology-group">
          <el-radio label="有" border />
          <el-radio label="无" border />
        </el-radio-group>
        <el-input
          v-if="form.p1Etiology === '有'"
          v-model="form.p1EtiologyDetail"
          placeholder="病因诱因"
          maxlength="50"
          show-word-limit
          class="p1-etiology-detail"
        />
      </el-form-item>
      <el-form-item v-if="form.p1 === '是'" prop="p1Features" label="特点">
        <el-checkbox-group v-model="form.p1Features">
          <el-checkbox label="排尿等待" border />
          <el-checkbox label="排尿无力" border />
          <el-checkbox label="尿线分叉" border />
          <el-checkbox label="终末尿液滴沥" border />
          <el-checkbox label="排尿中断" border />
          <el-checkbox label="夜尿增多" border />
        </el-checkbox-group>
      </el-form-item>
      <el-form-item prop="p2" label="2、尿频">
        <el-radio-group v-model="form.p2">
          <el-radio label="是" border />
          <el-radio label="否" border />
        </el-radio-group>
      </el-form-item>
      <el-form-item v-if="form.p2 === '是'" prop="p2Onset" label="起病时间">
        <el-date-picker
          v-model="form.p2Onset"
          type="datetime"
          value-format="yyyy-MM-dd HH:mm:ss"
          placeholder="请选择起病时间"
          :editable="false"
          style="width: 200px"
        />
      </el-form-item>
      <el-form-item v-if="form.p2 === '是'" prop="p2Pattern" label="尿频模式">
        <el-radio-group v-model="form.p2Pattern" class="p2-pattern-group">
          <el-radio label="全天型" border />
          <el-radio label="白昼型" border />
          <el-radio label="夜尿型" border />
          <el-radio label="其他" border />
        </el-radio-group>
        <el-input
          v-if="form.p2Pattern === '其他'"
          v-model="form.p2PatternOther"
          placeholder=""
          maxlength="20"
          show-word-limit
          class="p2-pattern-other"
        />
      </el-form-item>
      <el-form-item v-if="form.p2 === '是'" prop="p2Trend" label="变化趋势">
        <el-radio-group v-model="form.p2Trend" class="p2-trend-group">
          <el-radio label="加重" border />
          <el-radio label="缓解" border />
          <el-radio label="周期性发作" border />
          <el-radio label="其他" border />
        </el-radio-group>
        <el-input
          v-if="form.p2Trend === '其他'"
          v-model="form.p2TrendOther"
          placeholder=""
          maxlength="20"
          show-word-limit
          class="p2-trend-other"
        />
      </el-form-item>
      <el-form-item v-if="form.p2 === '是'" prop="p2Etiology" label="病因诱因">
        <el-radio-group v-model="form.p2Etiology" class="p2-etiology-group">
          <el-radio label="有" border />
          <el-radio label="无" border />
        </el-radio-group>
        <el-input
          v-if="form.p2Etiology === '有'"
          v-model="form.p2EtiologyDetail"
          placeholder="病因诱因"
          maxlength="50"
          show-word-limit
          class="p2-etiology-detail"
        />
      </el-form-item>
      <el-form-item prop="p3" label="3、尿急">
        <el-radio-group v-model="form.p3">
          <el-radio label="是" border />
          <el-radio label="否" border />
        </el-radio-group>
      </el-form-item>
      <el-form-item v-if="form.p3 === '是'" prop="p3Onset" label="起病时间">
        <el-date-picker
          v-model="form.p3Onset"
          type="datetime"
          value-format="yyyy-MM-dd HH:mm:ss"
          placeholder="请选择起病时间"
          :editable="false"
          style="width: 200px"
        />
      </el-form-item>
      <el-form-item v-if="form.p3 === '是'" prop="p3Trend" label="变化趋势">
          <el-radio-group v-model="form.p3Trend" class="p3-trend-group">
            <el-radio label="加重" border />
            <el-radio label="缓解" border />
            <el-radio label="周期性发作" border />
            <el-radio label="其他" border />
          </el-radio-group>
          <el-input
            v-if="form.p3Trend === '其他'"
            v-model="form.p3TrendOther"
            placeholder=""
            maxlength="20"
            show-word-limit
            class="p3-trend-other"
          />
      </el-form-item>
      <el-form-item v-if="form.p3 === '是'" prop="p3Etiology" label="病因诱因">
        <el-radio-group v-model="form.p3Etiology" class="p3-etiology-group">
          <el-radio label="有" border />
          <el-radio label="无" border />
        </el-radio-group>
        <el-input
          v-if="form.p3Etiology === '有'"
          v-model="form.p3EtiologyDetail"
          placeholder="病因诱因"
          maxlength="50"
          show-word-limit
          class="p3-etiology-detail"
        />
      </el-form-item>
      <el-form-item prop="p4" label="4、尿痛">
        <el-radio-group v-model="form.p4">
          <el-radio label="是" border />
          <el-radio label="否" border />
        </el-radio-group>
      </el-form-item>
      <el-form-item v-if="form.p4 === '是'" prop="p4Onset" label="起病时间">
        <el-date-picker
          v-model="form.p4Onset"
          type="datetime"
          value-format="yyyy-MM-dd HH:mm:ss"
          placeholder="请选择起病时间"
          :editable="false"
          style="width: 200px"
        />
      </el-form-item>
      <el-form-item v-if="form.p4 === '是'" prop="p4Location" label="尿痛部位">
        <el-radio-group v-model="form.p4Location" class="p4-location-group">
          <el-radio label="尿道口" border />
          <el-radio label="耻骨上" border />
          <el-radio label="肾区" border />
          <el-radio label="其他" border />
        </el-radio-group>
        <el-input
          v-if="form.p4Location === '其他'"
          v-model="form.p4LocationOther"
          placeholder=""
          maxlength="20"
          show-word-limit
          class="p4-location-other"
        />
      </el-form-item>
      <el-form-item v-if="form.p4 === '是'" prop="p4Trend" label="变化趋势">
        <el-radio-group v-model="form.p4Trend" class="p4-trend-group">
          <el-radio label="加重" border />
          <el-radio label="缓解" border />
          <el-radio label="周期性发作" border />
          <el-radio label="其他" border />
        </el-radio-group>
        <el-input
          v-if="form.p4Trend === '其他'"
          v-model="form.p4TrendOther"
          placeholder=""
          maxlength="20"
          show-word-limit
          class="p4-trend-other"
        />
      </el-form-item>
      <el-form-item v-if="form.p4 === '是'" prop="p4Etiology" label="病因诱因">
        <el-radio-group v-model="form.p4Etiology" class="p4-etiology-group">
          <el-radio label="有" border />
          <el-radio label="无" border />
        </el-radio-group>
        <el-input
          v-if="form.p4Etiology === '有'"
          v-model="form.p4EtiologyDetail"
          placeholder="病因诱因"
          maxlength="50"
          show-word-limit
          class="p4-etiology-detail"
        />
      </el-form-item>
      <el-form-item prop="p5" label="5、肉眼血尿">
        <el-radio-group v-model="form.p5">
          <el-radio label="是" border />
          <el-radio label="否" border />
        </el-radio-group>
      </el-form-item>
      <el-form-item prop="p6" label="6、骨痛">
        <el-radio-group v-model="form.p6">
          <el-radio label="是" border />
          <el-radio label="否" border />
        </el-radio-group>
      </el-form-item>
      <el-form-item prop="p7" label="7、体检发现PSA增高或影像学发现前列腺占位">
        <el-radio-group v-model="form.p7">
          <el-radio label="是" border />
          <el-radio label="否" border />
        </el-radio-group>
      </el-form-item>
    </el-form>
  </div>
</template>

<script>
export default {
  name: 'AN0158',
  props: {
    formData: String,
  },
  components: {},
  created() {
    if (this.formData != null && this.formData != '') {
      this.form = JSON.parse(this.formData);
      if (this.form.p1Onset === undefined) {
        this.$set(this.form, 'p1Onset', '');
      }
      if (this.form.p1Trend === undefined) {
        this.$set(this.form, 'p1Trend', '');
      }
      if (this.form.p1TrendOther === undefined) {
        this.$set(this.form, 'p1TrendOther', '');
      }
      if (this.form.p1Etiology === undefined) {
        this.$set(this.form, 'p1Etiology', '');
      }
      if (this.form.p1EtiologyDetail === undefined) {
        this.$set(this.form, 'p1EtiologyDetail', '');
      }
      if (this.form.p1Features === undefined) {
        this.$set(this.form, 'p1Features', []);
      }
      if (this.form.p2Onset === undefined) {
        this.$set(this.form, 'p2Onset', '');
      }
      if (this.form.p2Pattern === undefined) {
        this.$set(this.form, 'p2Pattern', '');
      }
      if (this.form.p2PatternOther === undefined) {
        this.$set(this.form, 'p2PatternOther', '');
      }
      if (this.form.p2Trend === undefined) {
        this.$set(this.form, 'p2Trend', '');
      }
      if (this.form.p2TrendOther === undefined) {
        this.$set(this.form, 'p2TrendOther', '');
      }
      if (this.form.p2Etiology === undefined) {
        this.$set(this.form, 'p2Etiology', '');
      }
      if (this.form.p2EtiologyDetail === undefined) {
        this.$set(this.form, 'p2EtiologyDetail', '');
      }
      if (this.form.p3Onset === undefined) {
        this.$set(this.form, 'p3Onset', '');
      }
      if (this.form.p3Trend === undefined) {
        this.$set(this.form, 'p3Trend', '');
      }
      if (this.form.p3TrendOther === undefined) {
        this.$set(this.form, 'p3TrendOther', '');
      }
      if (this.form.p3Etiology === undefined) {
        this.$set(this.form, 'p3Etiology', '');
      }
      if (this.form.p3EtiologyDetail === undefined) {
        this.$set(this.form, 'p3EtiologyDetail', '');
      }
      if (this.form.p4Onset === undefined) {
        this.$set(this.form, 'p4Onset', '');
      }
      if (this.form.p4Location === undefined) {
        this.$set(this.form, 'p4Location', '');
      }
      if (this.form.p4LocationOther === undefined) {
        this.$set(this.form, 'p4LocationOther', '');
      }
      if (this.form.p4Trend === undefined) {
        this.$set(this.form, 'p4Trend', '');
      }
      if (this.form.p4TrendOther === undefined) {
        this.$set(this.form, 'p4TrendOther', '');
      }
      if (this.form.p4Etiology === undefined) {
        this.$set(this.form, 'p4Etiology', '');
      }
      if (this.form.p4EtiologyDetail === undefined) {
        this.$set(this.form, 'p4EtiologyDetail', '');
      }
    }
  },
  data() {
    const validateP1Onset = (rule, value, callback) => {
      if (this.form.p1 === '是' && !value) {
        callback(new Error('请选择起病时间'));
      } else {
        callback();
      }
    };
    const validateP1Trend = (rule, value, callback) => {
      if (this.form.p1 === '是' && !value) {
        callback(new Error('请选择变化趋势'));
      } else {
        callback();
      }
    };
    const validateP1TrendOther = (rule, value, callback) => {
      if (this.form.p1 === '是' && this.form.p1Trend === '其他' && !value) {
        callback(new Error('请填写其他变化趋势'));
      } else {
        callback();
      }
    };
    const validateP1Etiology = (rule, value, callback) => {
      if (this.form.p1 === '是' && !value) {
        callback(new Error('请选择病因诱因'));
      } else {
        callback();
      }
    };
    const validateP1EtiologyDetail = (rule, value, callback) => {
      if (this.form.p1 === '是' && this.form.p1Etiology === '有' && !value) {
        callback(new Error('请填写病因诱因详情'));
      } else {
        callback();
      }
    };
    const validateP2Onset = (rule, value, callback) => {
      if (this.form.p2 === '是' && !value) {
        callback(new Error('请选择起病时间'));
      } else {
        callback();
      }
    };
    const validateP2Pattern = (rule, value, callback) => {
      if (this.form.p2 === '是' && !value) {
        callback(new Error('请选择尿频模式'));
      } else {
        callback();
      }
    };
    const validateP2PatternOther = (rule, value, callback) => {
      if (this.form.p2 === '是' && this.form.p2Pattern === '其他' && !value) {
        callback(new Error('请填写其他尿频模式'));
      } else {
        callback();
      }
    };
    const validateP2Trend = (rule, value, callback) => {
      if (this.form.p2 === '是' && !value) {
        callback(new Error('请选择变化趋势'));
      } else {
        callback();
      }
    };
    const validateP2TrendOther = (rule, value, callback) => {
      if (this.form.p2 === '是' && this.form.p2Trend === '其他' && !value) {
        callback(new Error('请填写其他变化趋势'));
      } else {
        callback();
      }
    };
    const validateP2Etiology = (rule, value, callback) => {
      if (this.form.p2 === '是' && !value) {
        callback(new Error('请选择病因诱因'));
      } else {
        callback();
      }
    };
    const validateP2EtiologyDetail = (rule, value, callback) => {
      if (this.form.p2 === '是' && this.form.p2Etiology === '有' && !value) {
        callback(new Error('请填写病因诱因详情'));
      } else {
        callback();
      }
    };
    const validateP3Onset = (rule, value, callback) => {
      if (this.form.p3 === '是' && !value) {
        callback(new Error('请选择起病时间'));
      } else {
        callback();
      }
    };
    const validateP3Trend = (rule, value, callback) => {
      if (this.form.p3 === '是' && !value) {
        callback(new Error('请选择变化趋势'));
      } else {
        callback();
      }
    };
    const validateP3TrendOther = (rule, value, callback) => {
      if (this.form.p3 === '是' && this.form.p3Trend === '其他' && !value) {
        callback(new Error('请填写其他变化趋势'));
      } else {
        callback();
      }
    };
    const validateP3Etiology = (rule, value, callback) => {
      if (this.form.p3 === '是' && !value) {
        callback(new Error('请选择病因诱因'));
      } else {
        callback();
      }
    };
    const validateP3EtiologyDetail = (rule, value, callback) => {
      if (this.form.p3 === '是' && this.form.p3Etiology === '有' && !value) {
        callback(new Error('请填写病因诱因详情'));
      } else {
        callback();
      }
    };
    const validateP4Onset = (rule, value, callback) => {
      if (this.form.p4 === '是' && !value) {
        callback(new Error('请选择起病时间'));
      } else {
        callback();
      }
    };
    const validateP4Location = (rule, value, callback) => {
      if (this.form.p4 === '是' && !value) {
        callback(new Error('请选择尿痛部位'));
      } else {
        callback();
      }
    };
    const validateP4LocationOther = (rule, value, callback) => {
      if (this.form.p4 === '是' && this.form.p4Location === '其他' && !value) {
        callback(new Error('请填写其他尿痛部位'));
      } else {
        callback();
      }
    };
    const validateP4Trend = (rule, value, callback) => {
      if (this.form.p4 === '是' && !value) {
        callback(new Error('请选择变化趋势'));
      } else {
        callback();
      }
    };
    const validateP4TrendOther = (rule, value, callback) => {
      if (this.form.p4 === '是' && this.form.p4Trend === '其他' && !value) {
        callback(new Error('请填写其他变化趋势'));
      } else {
        callback();
      }
    };
    const validateP4Etiology = (rule, value, callback) => {
      if (this.form.p4 === '是' && !value) {
        callback(new Error('请选择病因诱因'));
      } else {
        callback();
      }
    };
    const validateP4EtiologyDetail = (rule, value, callback) => {
      if (this.form.p4 === '是' && this.form.p4Etiology === '有' && !value) {
        callback(new Error('请填写病因诱因详情'));
      } else {
        callback();
      }
    };
    return {
      form: {
        p1: undefined,
        p1Onset: '',
        p1Trend: '',
        p1TrendOther: '',
        p1Etiology: '',
        p1EtiologyDetail: '',
        p1Features: [],
        p2: undefined,
        p2Onset: '',
        p2Pattern: '',
        p2PatternOther: '',
        p2Trend: '',
        p2TrendOther: '',
        p2Etiology: '',
        p2EtiologyDetail: '',
        p3: undefined,
        p3Onset: '',
        p3Trend: '',
        p3TrendOther: '',
        p3Etiology: '',
        p3EtiologyDetail: '',
        p4: undefined,
        p4Onset: '',
        p4Location: '',
        p4LocationOther: '',
        p4Trend: '',
        p4TrendOther: '',
        p4Etiology: '',
        p4EtiologyDetail: '',
        p5: undefined,
        p6: undefined,
        p7: undefined,
      },
      // 表单校验
      rules: {
        p1: [{ required: true, message: '必须选择', trigger: 'blur' }],
        p1Onset: [{ validator: validateP1Onset, trigger: 'change' }],
        p1Trend: [{ validator: validateP1Trend, trigger: 'change' }],
        p1TrendOther: [{ validator: validateP1TrendOther, trigger: 'blur' }],
        p1Etiology: [{ validator: validateP1Etiology, trigger: 'change' }],
        p1EtiologyDetail: [{ validator: validateP1EtiologyDetail, trigger: 'blur' }],
        p1Features: [],
        p2: [{ required: true, message: '必须选择', trigger: 'blur' }],
        p2Onset: [{ validator: validateP2Onset, trigger: 'change' }],
        p2Pattern: [{ validator: validateP2Pattern, trigger: 'change' }],
        p2PatternOther: [{ validator: validateP2PatternOther, trigger: 'blur' }],
        p2Trend: [{ validator: validateP2Trend, trigger: 'change' }],
        p2TrendOther: [{ validator: validateP2TrendOther, trigger: 'blur' }],
        p2Etiology: [{ validator: validateP2Etiology, trigger: 'change' }],
        p2EtiologyDetail: [{ validator: validateP2EtiologyDetail, trigger: 'blur' }],
        p3: [{ required: true, message: '必须选择', trigger: 'blur' }],
        p3Onset: [{ validator: validateP3Onset, trigger: 'change' }],
        p3Trend: [{ validator: validateP3Trend, trigger: 'change' }],
        p3TrendOther: [{ validator: validateP3TrendOther, trigger: 'blur' }],
        p3Etiology: [{ validator: validateP3Etiology, trigger: 'change' }],
        p3EtiologyDetail: [{ validator: validateP3EtiologyDetail, trigger: 'blur' }],
        p4: [{ required: true, message: '必须选择', trigger: 'blur' }],
        p4Onset: [{ validator: validateP4Onset, trigger: 'change' }],
        p4Location: [{ validator: validateP4Location, trigger: 'change' }],
        p4LocationOther: [{ validator: validateP4LocationOther, trigger: 'blur' }],
        p4Trend: [{ validator: validateP4Trend, trigger: 'change' }],
        p4TrendOther: [{ validator: validateP4TrendOther, trigger: 'blur' }],
        p4Etiology: [{ validator: validateP4Etiology, trigger: 'change' }],
        p4EtiologyDetail: [{ validator: validateP4EtiologyDetail, trigger: 'blur' }],
        p5: [{ required: true, message: '必须选择', trigger: 'blur' }],
        p6: [{ required: true, message: '必须选择', trigger: 'blur' }],
        p7: [{ required: true, message: '必须选择', trigger: 'blur' }],
      },
    };
  },
  methods: {
    /** 表单值 */
    getData() {
      return this.form;
    },
    validateData() {
      let rs = true;
      this.$refs['form'].validate((valid) => {
        if (valid) {
          rs = false;
        }
      });
      return rs;
    },
  },
  watch: {
    'form.p1'(val) {
      if (val !== '是') {
        this.form.p1Onset = '';
        this.form.p1Trend = '';
        this.form.p1TrendOther = '';
        this.form.p1Etiology = '';
        this.form.p1EtiologyDetail = '';
        this.form.p1Features = [];
        if (this.$refs.form) {
          this.$refs.form.clearValidate(['p1Onset', 'p1Trend', 'p1TrendOther', 'p1Etiology', 'p1EtiologyDetail', 'p1Features']);
        }
      }
    },
    'form.p1Trend'(val) {
      if (val !== '其他') {
        this.form.p1TrendOther = '';
        if (this.$refs.form) {
          this.$refs.form.clearValidate('p1TrendOther');
        }
      }
    },
    'form.p2'(val) {
      if (val !== '是') {
        this.form.p2Onset = '';
        this.form.p2Pattern = '';
        this.form.p2PatternOther = '';
        this.form.p2Trend = '';
        this.form.p2TrendOther = '';
        this.form.p2Etiology = '';
        this.form.p2EtiologyDetail = '';
        if (this.$refs.form) {
          this.$refs.form.clearValidate(['p2Onset', 'p2Pattern', 'p2PatternOther', 'p2Trend', 'p2TrendOther', 'p2Etiology', 'p2EtiologyDetail']);
        }
      }
    },
    'form.p2Pattern'(val) {
      if (val !== '其他') {
        this.form.p2PatternOther = '';
        if (this.$refs.form) {
          this.$refs.form.clearValidate('p2PatternOther');
        }
      }
    },
    'form.p2Trend'(val) {
      if (val !== '其他') {
        this.form.p2TrendOther = '';
        if (this.$refs.form) {
          this.$refs.form.clearValidate('p2TrendOther');
        }
      }
    },
    'form.p2Etiology'(val) {
      if (val !== '有') {
        this.form.p2EtiologyDetail = '';
        if (this.$refs.form) {
          this.$refs.form.clearValidate('p2EtiologyDetail');
        }
      }
    },
    'form.p3'(val) {
      if (val !== '是') {
        this.form.p3Onset = '';
        this.form.p3Trend = '';
        this.form.p3TrendOther = '';
        this.form.p3Etiology = '';
        this.form.p3EtiologyDetail = '';
        if (this.$refs.form) {
          this.$refs.form.clearValidate(['p3Onset', 'p3Trend', 'p3TrendOther', 'p3Etiology', 'p3EtiologyDetail']);
        }
      }
    },
    'form.p3Trend'(val) {
      if (val !== '其他') {
        this.form.p3TrendOther = '';
        if (this.$refs.form) {
          this.$refs.form.clearValidate('p3TrendOther');
        }
      }
    },
    'form.p3Etiology'(val) {
      if (val !== '有') {
        this.form.p3EtiologyDetail = '';
        if (this.$refs.form) {
          this.$refs.form.clearValidate('p3EtiologyDetail');
        }
      }
    },
    'form.p4'(val) {
      if (val !== '是') {
        this.form.p4Onset = '';
        this.form.p4Location = '';
        this.form.p4LocationOther = '';
        this.form.p4Trend = '';
        this.form.p4TrendOther = '';
        this.form.p4Etiology = '';
        this.form.p4EtiologyDetail = '';
        if (this.$refs.form) {
          this.$refs.form.clearValidate(['p4Onset', 'p4Location', 'p4LocationOther', 'p4Trend', 'p4TrendOther', 'p4Etiology', 'p4EtiologyDetail']);
        }
      }
    },
    'form.p4Location'(val) {
      if (val !== '其他') {
        this.form.p4LocationOther = '';
        if (this.$refs.form) {
          this.$refs.form.clearValidate('p4LocationOther');
        }
      }
    },
    'form.p4Trend'(val) {
      if (val !== '其他') {
        this.form.p4TrendOther = '';
        if (this.$refs.form) {
          this.$refs.form.clearValidate('p4TrendOther');
        }
      }
    },
    'form.p4Etiology'(val) {
      if (val !== '有') {
        this.form.p4EtiologyDetail = '';
        if (this.$refs.form) {
          this.$refs.form.clearValidate('p4EtiologyDetail');
        }
      }
    },
  },
};
export const formConfig = {
  fieldList: [ {
      key:"p1",
      label:"1、您是否存在慢性病史（例高血压、糖尿病）？"
    }, {
      key:"p2",
      label:"2、您是否存在膀胱癌家族史？"
    }, {
      key:"p3",
      label:"3、您是否存在吸烟史？"
    }, {
      key:"p4",
      label:"4、您是否存在化学品的接触史？"
    } ],
};
</script>
<style scoped>
::v-deep .el-radio--mini.is-bordered {
  padding: 6px 15px 0 15px !important;
  border-radius: 3px;
  height: 28px;
}

::v-deep .el-radio__input {
  display: none !important;
  white-space: nowrap;
  cursor: pointer;
  outline: 0;
  line-height: 1;
  vertical-align: middle;
}

::v-deep .el-radio__label {
  padding-left: 0px;
}

::v-deep .el-form-item__label {
  width: 100%;
  text-align: left;
  vertical-align: middle;
  float: left;
  color: #606266;
  line-height: 40px;
  padding: 0 12px 0 0;
  box-sizing: border-box;
  font-weight: 450;
  font-family: 'PingFangSC-Semibold', 'PingFang SC Semibold', 'PingFang SC', sans-serif;
  color: #000000;
}

::v-deep .el-radio-group {
  font-size: 0;
  /* //margin-top: 7px; */
}

::v-deep .el-radio {
  color: #000000;
  font-weight: 400;
  line-height: 1;
  cursor: pointer;
  white-space: nowrap;
  outline: 0;
  margin-right: 10px;
}

.p1-trend-group {
  display: inline-flex;
  align-items: center;
}

.p1-trend-other {
  width: 200px;
}

.p1-etiology-group {
  display: inline-flex;
  align-items: center;
}

.p1-etiology-detail {
  width: 300px;
  margin-left: 10px;
}

.p2-pattern-group {
  display: inline-flex;
  align-items: center;
}

.p2-pattern-other {
  width: 200px;
  margin-left: 10px;
}

.p2-trend-group {
  display: inline-flex;
  align-items: center;
}

.p2-trend-other {
  width: 200px;
  margin-left: 10px;
}

.p2-etiology-group {
  display: inline-flex;
  align-items: center;
}

.p2-etiology-detail {
  width: 300px;
  margin-left: 10px;
}

.p3-trend-group {
  display: inline-flex;
  align-items: center;
}

.p3-trend-other {
  width: 200px;
  margin-left: 10px;
}

.p3-etiology-group {
  display: inline-flex;
  align-items: center;
}

.p3-etiology-detail {
  width: 300px;
  margin-left: 10px;
}

.p4-location-group {
  display: inline-flex;
  align-items: center;
}

.p4-location-other {
  width: 200px;
  margin-left: 10px;
}

.p4-trend-group {
  display: inline-flex;
  align-items: center;
}

.p4-trend-other {
  width: 200px;
  margin-left: 10px;
}

.p4-etiology-group {
  display: inline-flex;
  align-items: center;
}

.p4-etiology-detail {
  width: 300px;
  margin-left: 10px;
}
</style>
